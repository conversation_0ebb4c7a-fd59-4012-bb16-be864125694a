import type {
  FormElement,
  FormField,
  Project,
  FieldMapping,
  FillResult
} from '~/types'
import type { FormFiller as IFormFiller } from '~/services/interfaces'

export class FormFiller implements IFormFiller {
  private readonly FIELD_MAPPINGS = {
    // 项目名称字段 -> Project.name
    name: ['name', 'title', 'app_name', 'product_name', 'project_name', 'software_name', 'app_title'],
    // 项目URL字段 -> Project.domain
    domain: ['url', 'website', 'site', 'link', 'homepage', 'demo_url', 'project_url', 'app_url'],
    // 项目描述字段 -> Project.info.introduction
    info: ['description', 'desc', 'about', 'details', 'info', 'summary', 'content', 'message', 'pitch'],
    // 项目分类字段 -> Project.category
    category: ['category', 'type', 'genre', 'classification', 'sector']
  }

  async fillForm(form: FormElement, project: Project, mapping?: FieldMapping): Promise<FillResult> {
    const result: FillResult = {
      success: false,
      filledFields: [],
      errors: [],
      warnings: []
    }

    try {
      // 使用提供的映射或创建自动映射
      const fieldMapping = mapping || this.createFieldMapping(form.fields, project)

      // 填充每个字段
      console.log(`开始填充表单: ${form}`)
      for (const field of form.fields) {
        try {
          const filled = await this.fillField(field, project, fieldMapping)
          if (filled) {
            result.filledFields.push(field.name)
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '未知错误'
          result.errors.push(`字段 ${field.name}: ${errorMessage}`)
        }
      }

      // 验证填充结果
      if (result.filledFields.length > 0) {
        result.success = true
        
        // 添加警告信息
        if (result.filledFields.length < form.fields.length) {
          result.warnings.push(`仅填充了 ${result.filledFields.length}/${form.fields.length} 个字段`)
        }
      } else {
        result.errors.push('没有字段被成功填充')
      }

      return result
    } catch (error) {
      result.errors.push(`填充表单失败: ${error instanceof Error ? error.message : '未知错误'}`)
      return result
    }
  }

  createFieldMapping(formFields: FormField[], project: Project): FieldMapping {
    const mapping: FieldMapping = {}

    for (const field of formFields) {
      const projectField = this.mapFieldToProject(field)
      if (projectField) {
        mapping[field.name] = {
          projectField,
          transform: this.createTransformFunction(field, projectField)
        }
      }
    }

    return mapping
  }

  validateFillResult(result: FillResult): boolean {
    return result.success && result.filledFields.length > 0 && result.errors.length === 0
  }

  private async fillField(field: FormField, project: Project, mapping: FieldMapping): Promise<boolean> {
    const fieldMapping = mapping[field.name]
    if (!fieldMapping) {
      return false
    }

    // 转换值
    let fillValue: string

    // 处理自定义映射（AI生成的内容）
    if (fieldMapping.projectField === 'custom') {
      if (fieldMapping.transform) {
        // AI生成的transform函数是闭包，不需要参数
        fillValue = fieldMapping.transform()
      } else {
        console.warn(`自定义字段 ${field.name} 缺少transform函数`)
        return false
      }
    } else {
      // 处理标准项目字段映射
      const projectValue = project[fieldMapping.projectField as keyof Project]
      if (projectValue === undefined || projectValue === null) {
        return false
      }

      if (fieldMapping.transform) {
        fillValue = fieldMapping.transform(projectValue)
      } else {
        fillValue = this.convertToString(projectValue, field)
      }
    }

    if (!fillValue) {
      return false
    }

    // 填充字段
    try {
      await this.setFieldValue(field, fillValue)
      return true
    } catch (error) {
      console.error(`填充字段 ${field.name} 失败:`, error)
      return false
    }
  }

  private mapFieldToProject(field: FormField): keyof Project | null {
    const searchText = `${field.name} ${field.label || ''} ${field.placeholder || ''}`.toLowerCase()

    // 使用字段映射表进行匹配
    for (const [projectField, keywords] of Object.entries(this.FIELD_MAPPINGS)) {
      if (keywords.some(keyword => searchText.includes(keyword))) {
        return projectField as keyof Project
      }
    }

    // 基于字段类型的启发式匹配
    switch (field.type) {
      case 'url':
        if (searchText.includes('demo') || searchText.includes('live')) {
          return 'domain'
        }
        break
      case 'email':
        // 邮箱字段通常不映射到项目信息
        return null
      case 'textarea':
        // 长文本字段优先映射到描述
        return 'info'
    }

    // 基于字段名称的模糊匹配
    if (searchText.includes('name') || searchText.includes('title')) {
      return 'name'
    }
    if (searchText.includes('url') || searchText.includes('link')) {
      return 'domain'
    }
    if (searchText.includes('desc') || searchText.includes('about')) {
      return 'info'
    }
    if (searchText.includes('category') || searchText.includes('type')) {
      return 'category'
    }

    return null
  }

  private createTransformFunction(field: FormField, projectField: keyof Project | 'custom'): ((value?: any) => string) | undefined {
    // 为特定字段类型创建转换函数
    if (projectField === 'info') {
      // 处理info字段，提取introduction
      return (info: any) => {
        if (info && typeof info === 'object' && info.introduction) {
          const text = String(info.introduction)
          if (field.maxLength && text.length > field.maxLength) {
            // 智能截断描述
            const truncated = text.substring(0, field.maxLength - 3)
            const lastSentence = truncated.lastIndexOf('。')
            const lastDot = truncated.lastIndexOf('.')
            const cutPoint = Math.max(lastSentence, lastDot)

            if (cutPoint > field.maxLength * 0.7) {
              return text.substring(0, cutPoint + 1)
            }

            return truncated + '...'
          }
          return text
        }
        return ''
      }
    }

    if (projectField === 'category' && field.maxLength) {
      // 根据字段最大长度截断文本
      return (text: string) => {
        const str = String(text || '')
        return str.length > field.maxLength! ? str.substring(0, field.maxLength! - 3) + '...' : str
      }
    }

    return undefined
  }

  private convertToString(value: any, field: FormField): string {
    if (value === null || value === undefined) {
      return ''
    }

    if (Array.isArray(value)) {
      // 数组值的处理
      if (field.type === 'select') {
        // 选择框只取第一个值
        return value.length > 0 ? String(value[0]) : ''
      } else {
        // 其他字段将数组连接为字符串
        return value.join(', ')
      }
    }

    if (typeof value === 'object') {
      // 特殊处理info对象，提取introduction
      if (value.introduction) {
        return String(value.introduction)
      }
      // 其他对象值转为JSON字符串
      return JSON.stringify(value)
    }

    return String(value)
  }

  private async setFieldValue(field: FormField, value: string): Promise<void> {
    const element = field.element

    // 聚焦元素
    element.focus()

    // 清空现有值
    if ('value' in element) {
      (element as HTMLInputElement).value = ''
    }

    // 根据字段类型设置值
    switch (field.type) {
      case 'select':
        await this.setSelectValue(element as HTMLSelectElement, value)
        break
      case 'textarea':
      case 'text':
      case 'email':
      case 'url':
        await this.setInputValue(element as HTMLInputElement | HTMLTextAreaElement, value)
        break
      case 'file':
        // 文件字段跳过
        throw new Error('不支持填充文件字段')
      default:
        await this.setInputValue(element as HTMLInputElement, value)
    }

    // 触发change事件
    element.dispatchEvent(new Event('input', { bubbles: true }))
    element.dispatchEvent(new Event('change', { bubbles: true }))
    
    // 失焦
    element.blur()
  }

  private async setSelectValue(select: HTMLSelectElement, value: string): Promise<void> {
    // 尝试精确匹配
    for (const option of select.options) {
      if (option.value === value || option.text === value) {
        select.value = option.value
        return
      }
    }

    // 尝试模糊匹配
    const lowerValue = value.toLowerCase()
    for (const option of select.options) {
      if (option.value.toLowerCase().includes(lowerValue) || 
          option.text.toLowerCase().includes(lowerValue)) {
        select.value = option.value
        return
      }
    }

    throw new Error(`选择框中没有匹配的选项: ${value}`)
  }

  private async setInputValue(input: HTMLInputElement | HTMLTextAreaElement, value: string): Promise<void> {
    // 模拟用户输入
    input.value = value

    // 对于某些框架，可能需要设置其他属性
    if ('_valueTracker' in input) {
      // React的处理
      (input as any)._valueTracker.setValue('')
    }

    // 模拟键盘输入事件（对于某些框架是必需的）
    const inputEvent = new Event('input', { bubbles: true })
    Object.defineProperty(inputEvent, 'target', {
      writable: false,
      value: input
    })
    
    input.dispatchEvent(inputEvent)
  }

  // 高级填充功能
  async previewFill(form: FormElement, project: Project, mapping?: FieldMapping): Promise<{
    fieldPreviews: Array<{
      field: FormField
      currentValue?: string
      proposedValue: string
      confidence: number
    }>
    overallConfidence: number
  }> {
    const fieldMapping = mapping || this.createFieldMapping(form.fields, project)
    const fieldPreviews: Array<{
      field: FormField
      currentValue?: string
      proposedValue: string
      confidence: number
    }> = []

    let totalConfidence = 0
    let mappedFields = 0

    for (const field of form.fields) {
      const currentValue = this.getCurrentFieldValue(field)
      const mappingInfo = fieldMapping[field.name]

      if (mappingInfo) {
        const projectValue = project[mappingInfo.projectField]
        let proposedValue: string

        if (mappingInfo.transform) {
          proposedValue = mappingInfo.transform(projectValue)
        } else {
          proposedValue = this.convertToString(projectValue, field)
        }

        const confidence = this.calculateMappingConfidence(field, mappingInfo.projectField, proposedValue)

        fieldPreviews.push({
          field,
          currentValue,
          proposedValue,
          confidence
        })

        totalConfidence += confidence
        mappedFields++
      }
    }

    const overallConfidence = mappedFields > 0 ? totalConfidence / mappedFields : 0

    return {
      fieldPreviews,
      overallConfidence
    }
  }

  private getCurrentFieldValue(field: FormField): string | undefined {
    const element = field.element
    
    if ('value' in element) {
      const value = (element as HTMLInputElement).value
      return value || undefined
    }

    return undefined
  }

  private calculateMappingConfidence(field: FormField, projectField: keyof Project | 'custom', proposedValue: string): number {
    let confidence = 0.5 // 基础分数

    // 处理自定义映射
    if (projectField === 'custom') {
      confidence = 0.8 // AI生成的内容给予较高置信度
    } else {
      // 基于字段名称匹配度
      const searchText = `${field.name} ${field.label || ''} ${field.placeholder || ''}`.toLowerCase()
      const keywords = this.FIELD_MAPPINGS[projectField as keyof typeof this.FIELD_MAPPINGS] || []

      if (keywords.some(keyword => searchText.includes(keyword))) {
        confidence += 0.3
      }

      // 基于字段类型匹配度
      if (projectField === 'domain' && field.type === 'url') {
        confidence += 0.2
      }
      if (projectField === 'info' && field.type === 'textarea') {
        confidence += 0.2
      }
    }

    // 基于内容质量
    if (proposedValue && proposedValue.length > 0) {
      confidence += 0.1
    }

    // 基于字段是否必填
    if (field.required) {
      confidence += 0.1
    }

    return Math.min(confidence, 1.0)
  }

  // 批量填充
  async fillMultipleForms(forms: FormElement[], project: Project): Promise<FillResult[]> {
    const results: FillResult[] = []

    for (const form of forms) {
      try {
        const result = await this.fillForm(form, project)
        results.push(result)
      } catch (error) {
        results.push({
          success: false,
          filledFields: [],
          errors: [`填充表单失败: ${error instanceof Error ? error.message : '未知错误'}`],
          warnings: []
        })
      }
    }

    return results
  }
}